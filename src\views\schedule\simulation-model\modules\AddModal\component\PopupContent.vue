<template>
  <div class="container">
    <div class="header">
      <div class="name">{{ item?.siteName }}</div>
      <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label"><span style="color:#1d2129;">{{ "流量: "}} </span>
        <a-input placeholder="请输入流量值" style="width: 80px; height: 20px;"  size="small" v-model="newItem.flow"/>
      </div>
      <div class="label" style="margin-top: 2px;"><span style="color:#1d2129;">{{ "水位: "}}</span>
        <a-input placeholder="请输入水位值" style="width: 80px;height: 20px;"  size="small" v-model="newItem.wlv"/>
      </div>
    </div>
    <div style="text-align: center; margin-bottom: 14px; height: 20px;">
      <a-button type="primary" size="small" style="height: 20px;" @click.stop="item.onProcessClick(newItem)">确定</a-button>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PopupContent',
    props: ['item'],
    data() {
      return {
        newItem:{
          flow:0,
          wlv:0,
        }
      }
    },
    created() {
      this.newItem.flow = this.item.flow;
      this.newItem.wlv = this.item.wlv;
      this.newItem.siteName = this.item.siteName;
    },
  }
</script>
<style lang="less">
  .mapboxgl-popup-content {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .container {
    width: 160px;
    max-height: 106px;
    position: relative;
    display: flex;
    flex-direction: column;
    .header {
      background: #5384fe;
      font-weight: 500;
      color: #FFF;
      line-height: 20px;
      font-size: 12px;
      padding: 4px 4px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        margin: 0 13px 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator {
      display: flex;
      flex-direction: column;
      padding: 5px 15px;
      .label {
        font-size: 12px;
        color: #4E5969;
      }
      .value {
        color: #1d2129;
      }
    }
  }
</style>
