<template>
  <div style="display: flex; flex-direction: column; background: #ffffff; margin-top: 2px;width: 100%;">
    <a-spin :spinning="spinning" tip="加载中......">
      <!-- 内容 -->
      <div style="position: relative; display: flex; flex-direction: row; height: 7.5rem; ">
        <!-- 地图 -->
        <div style="flex: 5; height: 100%;width: 100%;  position: relative">
          <MapBox @onMapMounted="onMapMounted" />
          <MapStyle v-if="!!mapIns" v-show="false" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />
          <!-- 水闸过程曲线 -->
          <div class="curve-panel" v-if="!!activeProcess">
            <div class="left">
              <div class="header">
                <div class="name">{{ activeProcess.projectName }}</div>
              </div>

              <div>
                <div class="indicator">
                  <div class="label">上游水位:</div>
                  <div class="value">{{ activeProcess.upWlv }}m</div>
                </div>
                <div class="indicator">
                  <div class="label">下游水位:</div>
                  <div class="value">{{ activeProcess.downWlv }}m</div>
                </div>
                <div class="indicator">
                  <div class="label">过闸流量:</div>
                  <div class="value">{{ activeProcess.q }}m³/s</div>
                </div>
              </div>

              <div style="text-align: center; margin-bottom: 10px">
                <a-button type="primary" size="small" @click.stop="activeProcess = null">收起曲线</a-button>
              </div>
            </div>
            <div class="right">
              <LineEchart :height="'210px'" :width="'100%'" :dataSource="lineChartDataShuiZha"
                :custom="lineChartCustomShuiZha">
              </LineEchart>
            </div>
          </div>
          <div style="width: 100%; height: 44px">
            <TimePlaySlider v-if="times.length && !!mapIns" :times="times" @onTimeChange="onTimeChange" />
          </div>
        </div>
        <div style="flex: 5; height: 100%; width: 100%; padding-left: 20px; display: flex; flex-direction: column">
          <!-- 表数据 -->
          <div style="flex: 39; width: 100%; height: 100%;">
            <ResultTable v-if="dataList" :dataSource="dataList" />
          </div>
          <!-- 折线图 -->
          <div style="flex: 22; position: relative; height: 100%; width: 100%;">
            <div style="position: absolute; top: 10px; left: 90px; width: 200px; height: 30px; z-index: 1000">
              <a-select v-model="hedaoName" allowClear
                style="width: 100%; height: 25px; font-weight: 400; font-size: 12px" placeholder="请选择"
                :options="hedaoOptions" show-search></a-select>
            </div>
            <LineEchart :height="'200px'" :width="'850px'" style="margin-top: 20px;" :dataSource="lineChartData1"
              ref="modelLineChartRef" :custom="lineChartCustom1"></LineEchart>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getOptions, getValueByKey } from '@/api/common'
import ResultTable from './component/ResultTable.vue'
import ProcessChart from './component/ProcessChart.vue'
import { getChSimPage, getInferRes, getChSimResList } from './services.js'
import MapBox from './component/MapBox/index.vue'
import MapStyle from './component/MapBox/MapStyle.vue'
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import { MapboxOverlay } from '@deck.gl/mapbox'
import initLayer from './component/initLayer'
import { mapboxPopup } from './component/popup.js'

import { mapBoundGeo } from '@/utils/mapBounds.js'
import axios from 'axios'
import { LineEchart } from '@/components/Echarts'
import MultiPolygon from '@/views/schedule/simulation-model/component/MapBox/MultiPolygon.vue'
import 'mapbox-gl/dist/mapbox-gl.css'
import { extractData, getChartsData } from './Utils.js'
import * as turf from '@turf/turf'
export default {
  name: 'ManualForecast',
  components: { MapBox, MapStyle, MultiPolygon, LineEchart, TimePlaySlider, ResultTable, ProcessChart },
  props: ['chSimId', 'modelId'],
  data() {
    return {
      spinning: false,
      mapIns: null,
      activeProcess: null,
      mapData: null,
      dataList: [],
      mapOverlayIns: null,
      times: [],
      geojson: null,
      showPopupItem: [],
      forecastOptions: [],
      dataSource: null,
      isTableExpanded: true,
      chartKey: 0,
      currentTime: null,
      lineChartData1: [],
      lineChartCustom1: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '0%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '65%',
        xAxisData: [],
      },
      columnsDrainage1: [
        { type: 'seq', title: '序号', width: 50 },
        {
          title: '时间',
          field: 'time',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
        {
          title: '水位(m)',
          field: 'wlevel',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
        {
          title: '流量(m³/s)',
          field: 'q',
          minWidth: 180,
          showOverflow: 'tooltip',
        },
      ],
      listDrainage1: [
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432755755, 30.452525076],
          wlevel: 4.265,
          q: 4.1234,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432755755, 30.452525076],
          wlevel: 4.265,
          q: 4.1234,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432755755, 30.452525076],
          wlevel: 4.265,
          q: 4.1234,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432755755, 30.452525076],
          wlevel: 4.265,
          q: 4.1234,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432755755, 30.452525076],
          wlevel: 4.265,
          q: 4.1234,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432579421, 30.454064999],
          wlevel: 4.265,
          q: 5.4824,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.432038747, 30.455509059],
          wlevel: 4.265,
          q: 7.0043,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.431196589, 30.456877102],
          wlevel: 4.265,
          q: 12.617,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.430385053, 30.458257812],
          wlevel: 4.265,
          q: 12.6087,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.429671994, 30.459679297],
          wlevel: 4.265,
          q: 12.5991,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.429101548, 30.46081647],
          wlevel: 4.265,
          q: 15.827,
        },
        {
          time: '2025-06-12 08:00:00',
          coords: [120.428531101, 30.46195363],
          wlevel: 4.265,
          q: 19.059,
        },
      ],
      hedaoOptions: [],
      hedaoName: '',
      lineOptions: [],
      lineChartCustomShuiZha: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        rYInverse: false, // 右侧y轴是否反向
        yNameLocation: "end",
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '1%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '20%',
        xAxisData: []
      },
      lineChartDataShuiZha: []
    }
  },
  watch: {
    hedaoName(newVal, oldVal) {
      if (newVal !== oldVal) {
        let { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ } = getChartsData(newVal, this.currentTime, this.modelData)
        let res = [
          {
            name: '水位(m)',
            color: '#507EF7',
            yAxisIndex: 0,
            data: wlevel,
          },
          {
            name: '流量(m³/s)',
            color: '#B5E241',
            yAxisIndex: 1,
            data: q,
          },
        ]
        this.lineChartCustom1.xAxisData = stakes
        this.lineChartCustom1.yMax0 = maxWlevel
        this.lineChartCustom1.yMin0 = minWlevel
        this.lineChartCustom1.yMax1 = maxQ
        this.lineChartCustom1.yMin1 = minQ
        this.lineChartData1 = res
      }
    },
    chSimId(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (!this.chSimId) return
        this.init()
      }
    },
    currentTime(newVal, oldVal) { // 时间轴改变时，重新获取数据
      if (newVal !== oldVal) {
        if (!this.mapIns || !this.modelData) return
        let { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ } = getChartsData(this.hedaoName, newVal, this.modelData)
        let res = [
          {
            name: '水位(m)',
            color: '#507EF7',
            yAxisIndex: 0,
            data: wlevel,
          },
          {
            name: '流量(m³/s)',
            color: '#B5E241',
            yAxisIndex: 1,
            data: q,
          },
        ]
        this.lineChartCustom1.xAxisData = stakes
        this.lineChartCustom1.yMax0 = maxWlevel
        this.lineChartCustom1.yMin0 = minWlevel
        this.lineChartCustom1.yMax1 = maxQ
        this.lineChartCustom1.yMin1 = minQ
        this.lineChartData1 = res
      }
    }
  },
  created() {
    if (this.chSimId) this.init()
  },

  methods: {
    async init() {
      this.spinning = true
      // 根据模型ID获取右上角表格数据
      await getInferRes({ chSimId: parseInt(this.chSimId) })
        .then(res => {
          if (res.data.length > 0) {
            res.data.forEach(element => {
              element['projects'] = element['resVOS'][0].records
            })
            console.log(res.data)
            this.initTimesAndData(res.data)
          }
        })

      await getOptions('scaleProjectCode').then(res => {
        this.showPopupItem = res.data.map(el => ({ projectCode: el.key, projectName: el.value }))
        this.onTimeChange(this.currentTime)
      })

      await axios.get(`${process.env.VUE_APP_MODEL_DATA}/${this.modelId}.json`).then(res => {
        this.modelData = res.data
        extractData(res.data) // 初始化不同水渠中的最大值与最小值
        this.hedaoName = this.hedaoOptions[0].value
      }).catch(err => {
        console.log(err)
      }).finally(() => {
        this.spinning = false
      })

    },
    initTimesAndData(scaleData) {
      this.dataList = scaleData
      this.mapData = scaleData
      this.times = [...new Set(this.mapData[0].resVOS.map(el => el.tm))]
      this.hedaoOptions = scaleData.map(el => ({ label: el.projectName, value: el.projectCode }))
    },
    headerCellClassName({ column }) {
      return 'col-blue'
    },
    rowClassName() {
      return 'row-green'
    },
    onMapMounted(mapIns) {
      this.mapIns = mapIns
      this.$nextTick(() => {
        this.mapIns.resize()
      })
      this.mapOverlayIns = new MapboxOverlay({
        id: 'deck-geojson-layer-overlay',
        layers: [],
      })
      this.mapIns.addControl(this.mapOverlayIns)
      this.dealLayers()
      // this.getData(mapIns)
    },
    goToComingWaterForecast() {
      this.$router.push('/schedule/simulation-model-case')
    },
    onTimeChange(time) {
      this.currentTime = time

      if (this.mapData.length > 0) {
        let arr = []
        this.mapData.forEach(el => {
          arr = arr.concat(el.resVOS.find(ele => ele.tm == time)?.records)
        })
        const factArr = arr.filter(el => !!+el?.longitude && !!+el?.latitude)
        if (factArr.length === 0) return
        this.geojson = {
          type: 'FeatureCollection',
          features: factArr.map(el => {
            return {
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [+el.longitude, +el.latitude],
              },
              properties: {
                ...el,
              },
            }
          }),
        }

        if (!this.mapOverlayIns?._props?.layers?.length > 0) {
          this.dealLayers()
        }

        this.handleOpenPopup(factArr)

        if (!!this.activeProcess) {
          this.activeProcess = {
            chartData: this.activeProcess.chartData,
            ...factArr.find(el => el.projectId === this.activeProcess.projectId),
          }
        }
      }
    },
    handleOpenPopup(factArr) {
      if (this.showPopupItem.length > 0 && !!this.mapIns) {
        this.showPopupItem.forEach(el => {
          el?.popupIns?.remove()

          if (factArr.some(ele => ele.projectCode === el.projectCode)) {
            this.dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
          }
        })
      }
    },
    dealLayers() {
      if (!!this.mapIns && !!this.geojson) {
        initLayer(this.mapIns, this.mapOverlayIns, this.geojson, item => {
          if (this.showPopupItem.every(el => el.projectCode !== item.projectCode)) {
            this.dealPopup(item)
          }
        })
      }
    },
    dealPopup(curr) {
      const popupIns = mapboxPopup(this.mapIns, {
        ...curr,
        lngLat: [+curr.longitude, +curr.latitude],
        onPopupClose: item => {
          const index = this.showPopupItem.findIndex(el => el.projectCode === item.projectCode)
          this.showPopupItem[index].popupIns.remove()
          this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
        },
        onProcessClick: item => {
          getChSimResList({ chSimId: this.chSimId, projectCode: item.projectCode }).then(res => {
            let times = res.data.map(el => el.tm)
            let downWlv = []
            let q = []
            let upWlv = []
            let curdownWlv = ''
            let curupWlv = ''
            let curq = ''
            res.data.forEach((element, index) => {
              downWlv.push(element?.downWlv)
              q.push(+(element?.q))
              upWlv.push(+(element?.upWlv))
              if (element?.tm == this.currentTime) {
                curdownWlv = element?.downWlv
                curupWlv = element?.upWlv
                curq = element?.q
              }
            });

            let chartData = [{
              name: '上游水位',
              color: '#507EF7',
              yAxisIndex: 0,
              data: upWlv
            },
            {
              name: '过闸流量',
              color: '#F7BA1E',
              yAxisIndex: 1,
              data: q
            },
            {
              name: '下游水位',
              color: '#B5E241',
              yAxisIndex: 0,
              data: downWlv
            }]
            this.lineChartCustomShuiZha.xAxisData = times
            this.lineChartCustomShuiZha.yMax0 = Math.max(...upWlv, ...downWlv)
            this.lineChartCustomShuiZha.yMin0 = Math.min(...upWlv, ...downWlv)
            this.lineChartCustomShuiZha.yMax1 = Math.max(...q)
            this.lineChartCustomShuiZha.yMin1 = Math.min(...q)
            this.lineChartDataShuiZha = chartData

            this.activeProcess = { downWlv: curdownWlv || 0, upWlv: curupWlv || 0, q: curq || 0, projectName: item.projectName }
          })
        },
      })

      popupIns.getElement().style['z-index'] = '11'

      let index = this.showPopupItem.findIndex(el => el.projectCode === curr.projectCode)
      if (index === -1) {
        this.showPopupItem.push({ projectCode: curr.projectCode, popupIns })
      } else {
        this.showPopupItem[index] = { ...this.showPopupItem[index], popupIns }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.summary {
  display: flex;
  justify-content: space-between;

  .hgroup {
    width: 19%;
    padding: 24px 26px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;

    .content {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .num {
      font-weight: 700;
      font-size: 24px;
      color: #1d2129;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0;
    }

    .text {
      font-size: 14px;
      color: #4e5969;
      font-weight: 400;
      margin: 0;
    }

    .unit {
      margin-left: -2px;
      font-size: 14px;
    }

    .icon {
      width: 50px;
      height: 50px;
      display: inline-block;
      flex-shrink: 0;
    }

    &:nth-child(1) {
      .icon {
        background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(2) {
      .icon {
        background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(3) {
      .icon {
        background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(4) {
      .icon {
        background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }

    &:nth-child(5) {
      .icon {
        background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
        background-size: 100%;
      }

      .num {
        font-size: 15px;
      }
    }
  }
}

.flood-box {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  .flood-tabs {
    margin: 0;

    .name {
      font-size: 20px;
      color: #1d2129;
      font-weight: 600;
    }
  }

  .flood-content {
    flex: 1;
  }
}

@font-face {
  font-family: 'AlimamaDaoLiTi';
  src: url('@/assets/font/AlimamaDaoLiTi.ttf');
}

.curve-panel {
  position: absolute;
  z-index: 1000;
  bottom: 4px;
  right: 4px;
  width: 700px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;

  .left {
    border-right: 1px solid #e5e6eb;
    width: 150px;
    position: relative;
    display: flex;
    flex-direction: column;

    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }

      .name {
        flex: 1;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .indicator {
      display: flex;
      padding: 6px 8px;
      justify-content: space-between;

      .label {
        color: '#4E5969';
      }

      .value {
        color: #1d2129;
      }
    }
  }

  .right {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
